import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'
import { AttendanceType } from '@/lib/domain/entities/absence'
import { validateAttendanceTypeAccess } from '@/lib/utils/attendance-validation'
import { authenticateRequest } from '@/lib/middleware/hybrid-auth-middleware'
import { verifyToken } from '@/lib/utils/auth'
import { serverConfig } from '@/lib/config'
import { NotFoundError, ValidationError, DuplicateError } from '@/lib/domain/errors'
import { createAbsenceUseCases } from '@/lib/services/absence-service-factory'

// Initialize dependencies with prayer exemption support
const absenceUseCases = createAbsenceUseCases()

// Schema for single date entry (backward compatibility)
const manualEntrySchema = z.object({
  uniqueCode: z.string().uuid('Invalid unique code format'),
  type: z.nativeEnum(AttendanceType, { message: 'Invalid attendance type' }),
  recordedAt: z.string().datetime('Invalid datetime format'),
  reason: z.string().optional(),
  force: z.boolean().optional(), // Allow force update from frontend
})

// Schema for date range entry (new functionality)
const manualEntryRangeSchema = z.object({
  uniqueCode: z.string().uuid('Invalid unique code format'),
  type: z.nativeEnum(AttendanceType, { message: 'Invalid attendance type' }),
  startDate: z.string().datetime('Invalid start date format'),
  endDate: z.string().datetime('Invalid end date format'),
  reason: z.string().optional(),
  force: z.boolean().optional(), // Allow force update from frontend
})

// Union schema to support both single date and date range
const manualEntryUnionSchema = z.union([manualEntrySchema, manualEntryRangeSchema])

export async function POST(request: NextRequest) {
  try {
    // ✅ HYBRID AUTH: Authenticate with JWT + Redis session validation
    const authResult = await authenticateRequest(request, 'admin')

    if (!authResult.isValid) {
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 })
    }

    const adminRole = authResult.role as 'admin' | 'super_admin' | 'teacher' | 'receptionist'

    // Parse and validate request body
    const body = await request.json()
    const validatedData = manualEntryUnionSchema.parse(body)

    // Validate that the user role can handle this attendance type
    if (!validateAttendanceTypeAccess(adminRole, validatedData.type)) {
      return NextResponse.json(
        { message: 'Anda tidak memiliki akses untuk mencatat jenis absensi ini.' },
        { status: 403 }
      )
    }

    // Check if reason is required for certain attendance types
    const requiresReason = [
      AttendanceType.TEMPORARY_LEAVE,
      AttendanceType.EXCUSED_ABSENCE,
      AttendanceType.SICK,
    ].includes(validatedData.type)
    if (requiresReason && !validatedData.reason?.trim()) {
      return NextResponse.json(
        { message: 'Alasan diperlukan untuk jenis absensi ini. Silakan isi kolom alasan.' },
        { status: 400 }
      )
    }

    // Check if this is a date range request or single date request
    const isDateRange = 'startDate' in validatedData && 'endDate' in validatedData

    if (isDateRange) {
      // Handle date range request
      console.log('Manual attendance range request:', {
        uniqueCode: validatedData.uniqueCode,
        type: validatedData.type,
        startDate: validatedData.startDate,
        endDate: validatedData.endDate,
        force: validatedData.force || false,
      })

      // Validate that date range is only supported for SICK and EXCUSED_ABSENCE
      if (
        validatedData.type !== AttendanceType.SICK &&
        validatedData.type !== AttendanceType.EXCUSED_ABSENCE
      ) {
        return NextResponse.json(
          { message: 'Rentang tanggal hanya didukung untuk jenis absensi Sakit dan Izin.' },
          { status: 400 }
        )
      }

      // Record the attendance range using the initialized use cases
      const result = await absenceUseCases.recordAbsenceRange(
        validatedData.uniqueCode,
        validatedData.type,
        new Date(validatedData.startDate),
        new Date(validatedData.endDate),
        validatedData.force || false,
        validatedData.reason
      )

      return NextResponse.json({
        message: `Absensi berhasil dicatat untuk ${result.totalDays} hari`,
        result: {
          totalDays: result.totalDays,
          successCount: result.successes.length,
          failureCount: result.failures.length,
          successes: result.successes.map(absence => ({
            id: absence.id,
            type: absence.type,
            recordedAt: absence.recordedAt,
          })),
          failures: result.failures.map(failure => ({
            date: failure.date,
            error: failure.error,
          })),
        },
      })
    } else {
      // Handle single date request (backward compatibility)
      console.log('Manual attendance single request:', {
        uniqueCode: validatedData.uniqueCode,
        type: validatedData.type,
        force: validatedData.force || false,
        recordedAt: validatedData.recordedAt,
      })

      // Record the attendance using the initialized use cases
      const absence = await absenceUseCases.recordAbsence(
        validatedData.uniqueCode,
        validatedData.type,
        validatedData.force || false, // Use force flag from frontend, default to false
        validatedData.reason // Pass reason to use case
      )

      return NextResponse.json({
        message: 'Absensi berhasil dicatat',
        absence: {
          id: absence.id,
          type: absence.type,
          recordedAt: absence.recordedAt,
        },
      })
    }
  } catch (error) {
    console.error('Error recording manual attendance:', error)

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          message: 'Data yang dikirim tidak valid. Silakan periksa kembali form Anda.',
          errors: error.errors,
        },
        { status: 400 }
      )
    }

    if (error instanceof NotFoundError) {
      return NextResponse.json(
        { message: 'Siswa tidak ditemukan. Silakan periksa kode unik siswa.' },
        { status: 404 }
      )
    }

    if (error instanceof ValidationError) {
      return NextResponse.json(
        {
          message: 'Data yang dikirim tidak valid. Silakan periksa kembali form Anda.',
          error: error.message,
        },
        { status: 400 }
      )
    }

    if (error instanceof DuplicateError) {
      return NextResponse.json(
        {
          message:
            'Absensi sudah tercatat untuk hari ini. Gunakan tombol "Perbarui Absensi" untuk mengubah data yang sudah ada.',
          error: error.message,
        },
        { status: 409 }
      )
    }

    console.error('Unexpected error in manual attendance:', error)
    return NextResponse.json(
      {
        message:
          'Terjadi kesalahan pada server. Silakan coba lagi atau hubungi administrator jika masalah berlanjut.',
        error: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    )
  }
}
