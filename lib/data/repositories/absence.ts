import { eq, and, gte, lte, desc, inArray } from 'drizzle-orm'
import { db, schema } from '../drizzle/db'
import {
  Absence,
  AttendanceType,
  AttendanceSummary,
  CreateAbsenceDTO,
  getAttendanceTypesByReportType,
} from '../../domain/entities/absence'
import { formatTimeWITA, getCurrentWITATime } from '../../utils/date'
import { TIMEZONE_CONFIG } from '../../config'

// Interface for updating an absence
interface UpdateAbsenceDTO {
  recordedAt: Date
  reason?: string // Optional reason for attendance
}

/**
 * Absence repository implementation
 */
export class AbsenceRepository {
  /**
   * Find an absence by unique code, type, and date
   */
  async findByUniqueCodeAndTypeAndDate(
    uniqueCode: string,
    type: AttendanceType,
    date: Date
  ): Promise<Absence | null> {
    // Ensure we're working with a valid date
    if (!date || isNaN(date.getTime())) {
      console.error('Invalid date provided to findByUniqueCodeAndTypeAndDate:', date)
      return null
    }

    // Create a new date object to avoid modifying the original
    const startOfDay = new Date(date)
    startOfDay.setHours(0, 0, 0, 0)

    const endOfDay = new Date(date)
    endOfDay.setHours(23, 59, 59, 999)

    // Convert dates to ISO strings for SQL
    const startDateStr = startOfDay.toISOString()
    const endDateStr = endOfDay.toISOString()

    const [absence] = await db
      .select()
      .from(schema.absences)
      .where(
        and(
          eq(schema.absences.uniqueCode, uniqueCode),
          eq(schema.absences.type, type),
          gte(schema.absences.recordedAt, new Date(startDateStr)),
          lte(schema.absences.recordedAt, new Date(endDateStr))
        )
      )
      .limit(1)

    if (!absence) {
      return null
    }

    return this.mapToAbsence(absence)
  }

  /**
   * Create a new absence record
   */
  async create(data: CreateAbsenceDTO): Promise<Absence> {
    const [absence] = await db
      .insert(schema.absences)
      .values({
        uniqueCode: data.uniqueCode,
        type: data.type,
        recordedAt: data.recordedAt,
        createdAt: getCurrentWITATime(),
        reason: data.reason || null, // Store reason if provided
      })
      .returning()

    return this.mapToAbsence(absence)
  }

  /**
   * Get attendance summary for reporting (OPTIMIZED VERSION with database-level filtering)
   */
  async getAttendanceSummary(
    date?: Date,
    className?: string,
    reportType: 'prayer' | 'school' | 'all' = 'all'
  ): Promise<AttendanceSummary[]> {
    try {
      // Set default date to today if not provided
      if (!date) {
        date = new Date()
      }

      // Create date range based on filter type
      let startOfDay: Date
      let endOfDay: Date

      {
        // For day filter, get data for the specific day
        // Use centralized timezone configuration
        const witaTime = new Intl.DateTimeFormat('en-CA', {
          timeZone: TIMEZONE_CONFIG.TIMEZONE,
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit',
          second: '2-digit',
          hour12: false,
        }).formatToParts(date || new Date())

        const year = parseInt(witaTime.find(part => part.type === 'year')?.value || '0')
        const month = parseInt(witaTime.find(part => part.type === 'month')?.value || '1') - 1
        const day = parseInt(witaTime.find(part => part.type === 'day')?.value || '1')

        // ✅ FIX: Create date range correctly for WITA timezone
        // WITA is UTC+8, so we need to create UTC dates that represent WITA day boundaries

        // Create start of day in WITA (00:00:00 WITA = 16:00:00 previous day UTC)
        const utcStartOfDay = new Date(Date.UTC(year, month, day - 1, 16, 0, 0, 0))

        // Create end of day in WITA (23:59:59 WITA = 15:59:59 current day UTC)
        const utcEndOfDay = new Date(Date.UTC(year, month, day, 15, 59, 59, 999))

        // UTC dates represent WITA day boundaries correctly

        startOfDay = utcStartOfDay
        endOfDay = utcEndOfDay

        // ✅ FIXED: Daily filter uses correct UTC range for WITA timezone
      }

      // Format dates as ISO strings for SQL
      const startDateStr = startOfDay.toISOString()
      const endDateStr = endOfDay.toISOString()

      // Build the query conditions
      const conditions = [eq(schema.users.role, 'student')]

      // Add class filter if provided
      if (className) {
        conditions.push(eq(schema.classes.name, className))
      }

      console.log('OPTIMIZED QUERY: Using single JOIN query instead of individual processing')
      const startTime = Date.now()

      // OPTIMIZED: Single query with aggregate functions
      // First get all students with complete information
      const allStudents = await db
        .select({
          uniqueCode: schema.users.uniqueCode,
          name: schema.users.name,
          className: schema.classes.name,
          nis: schema.users.nis,
          gender: schema.users.gender,
        })
        .from(schema.users)
        .leftJoin(schema.classes, eq(schema.users.classId, schema.classes.id))
        .where(and(...conditions))

      console.log(`Found ${allStudents.length} students`)

      // Build attendance query conditions with database-level filtering
      const attendanceConditions = [
        gte(schema.absences.recordedAt, new Date(startDateStr)),
        lte(schema.absences.recordedAt, new Date(endDateStr)),
      ]

      // Add reportType filtering at database level using SINGLE SOURCE OF TRUTH
      if (reportType !== 'all') {
        // Use centralized function to get attendance types
        const allowedTypes = getAttendanceTypesByReportType(reportType)

        // ✅ FIXED: Validate that allowedTypes is not empty before using inArray
        if (allowedTypes && allowedTypes.length > 0) {
          attendanceConditions.push(inArray(schema.absences.type, allowedTypes))
          console.log(`🔍 DATABASE FILTER: Applied ${reportType} types filter:`, allowedTypes)
        } else {
          console.warn(`⚠️ WARNING: No attendance types found for reportType: ${reportType}`)
        }
      }
      // For 'all' reportType, no additional filtering needed

      // Execute optimized query with database-level filtering
      const attendanceRecords = await db
        .select({
          uniqueCode: schema.absences.uniqueCode,
          type: schema.absences.type,
          recordedAt: schema.absences.recordedAt,
        })
        .from(schema.absences)
        .where(and(...attendanceConditions))

      console.log(`Found ${attendanceRecords.length} attendance records`)

      const queryTime = Date.now() - startTime
      console.log(`OPTIMIZED QUERY completed in ${queryTime}ms`)

      // Create attendance map for quick lookup
      const attendanceMap = new Map<string, any[]>()
      attendanceRecords.forEach(record => {
        if (!attendanceMap.has(record.uniqueCode)) {
          attendanceMap.set(record.uniqueCode, [])
        }
        attendanceMap.get(record.uniqueCode)!.push(record)
      })

      // Transform the result to AttendanceSummary format
      const summaries: AttendanceSummary[] = allStudents.map(student => {
        const studentAttendance = attendanceMap.get(student.uniqueCode || '') || []

        // Initialize attendance flags
        let zuhr = false,
          asr = false,
          dismissal = false,
          ijin = false,
          ijinZuhr = false,
          ijinAsr = false
        let entry = false,
          lateEntry = false,
          excusedAbsence = false
        let temporaryLeave = false,
          returnFromLeave = false,
          sick = false

        // Initialize time tracking
        let zuhrTime: string | null = null,
          asrTime: string | null = null
        let dismissalTime: string | null = null,
          ijinTime: string | null = null,
          ijinZuhrTime: string | null = null,
          ijinAsrTime: string | null = null
        let entryTime: string | null = null,
          lateEntryTime: string | null = null
        let excusedAbsenceTime: string | null = null,
          temporaryLeaveTime: string | null = null
        let returnFromLeaveTime: string | null = null,
          sickTime: string | null = null

        let latestRecordedAt: Date | null = null

        // Process attendance records
        studentAttendance.forEach(record => {
          // Use single source of truth for time formatting
          const timeString = formatTimeWITA(record.recordedAt, { includeSeconds: true })

          switch (record.type) {
            case 'Zuhr':
              zuhr = true
              zuhrTime = timeString
              break
            case 'Asr':
              asr = true
              asrTime = timeString
              break
            case 'Pulang':
              dismissal = true
              dismissalTime = timeString
              break
            case 'Ijin':
              ijin = true
              ijinTime = timeString
              break
            case 'Entry':
              entry = true
              entryTime = timeString
              break
            case 'Late Entry':
              lateEntry = true
              lateEntryTime = timeString
              break
            case 'Excused Absence':
              excusedAbsence = true
              excusedAbsenceTime = timeString
              break
            case 'Temporary Leave':
              temporaryLeave = true
              temporaryLeaveTime = timeString
              break
            case 'Return from Leave':
              returnFromLeave = true
              returnFromLeaveTime = timeString
              break
            case 'Sick':
              sick = true
              sickTime = timeString
              break
          }

          if (!latestRecordedAt || record.recordedAt > latestRecordedAt) {
            latestRecordedAt = record.recordedAt
          }
        })

        return {
          summaryDate: latestRecordedAt || startOfDay,
          uniqueCode: student.uniqueCode || 'unknown',
          name: student.name || 'Unknown',
          className: student.className || 'Belum ditentukan',
          nis: student.nis || '',
          gender: student.gender || '',

          // Prayer attendance with actual timestamps
          zuhr,
          zuhrTime,
          asr,
          asrTime,
          dismissal,
          dismissalTime,
          ijin,
          ijinTime,
          ijinZuhr,
          ijinZuhrTime,
          ijinAsr,
          ijinAsrTime,

          // School attendance with actual timestamps
          entry,
          entryTime,
          lateEntry,
          lateEntryTime,
          excusedAbsence,
          excusedAbsenceTime,
          temporaryLeave,
          temporaryLeaveTime,
          returnFromLeave,
          returnFromLeaveTime,
          sick,
          sickTime,

          updatedAt: latestRecordedAt || startOfDay,
        }
      })

      console.log(
        `ATTENDANCE SUMMARY COMPLETE: Total students processed: ${summaries.length}, Query time: ${queryTime}ms`
      )

      return summaries
    } catch (error) {
      console.error('Error in getAttendanceSummary:', error)
      return []
    }
  }

  /**
   * Find all absences by unique code and date
   */
  async findByUniqueCodeAndDate(uniqueCode: string, date: Date): Promise<Absence[]> {
    try {
      // Ensure we have a valid date
      if (!date || isNaN(date.getTime())) {
        console.error('Invalid date provided to findByUniqueCodeAndDate:', date)
        return []
      }

      // Create date range for the day
      const startOfDay = new Date(date)
      startOfDay.setHours(0, 0, 0, 0)

      const endOfDay = new Date(date)
      endOfDay.setHours(23, 59, 59, 999)

      // Format dates as ISO strings for SQL
      const startDateStr = startOfDay.toISOString()
      const endDateStr = endOfDay.toISOString()

      // Query the database
      const absences = await db
        .select()
        .from(schema.absences)
        .where(
          and(
            eq(schema.absences.uniqueCode, uniqueCode),
            gte(schema.absences.recordedAt, new Date(startDateStr)),
            lte(schema.absences.recordedAt, new Date(endDateStr))
          )
        )

      // Map the results to domain entities
      return absences.map(absence => this.mapToAbsence(absence))
    } catch (error) {
      console.error('Error in findByUniqueCodeAndDate:', error)
      return []
    }
  }

  /**
   * Refresh the materialized view for attendance summary
   */
  async refreshMaterializedView(): Promise<void> {
    // In a real implementation, this would refresh the materialized view
    // For now, we'll just do nothing
  }

  /**
   * Update an existing absence record
   */
  async update(id: number, data: UpdateAbsenceDTO): Promise<Absence> {
    const [absence] = await db
      .update(schema.absences)
      .set({
        recordedAt: data.recordedAt,
        reason: data.reason !== undefined ? data.reason : undefined, // Update reason if provided
      })
      .where(eq(schema.absences.id, id))
      .returning()

    return this.mapToAbsence(absence)
  }

  /**
   * Delete an absence record by unique code, type, and date
   */
  async deleteByUniqueCodeAndTypeAndDate(
    uniqueCode: string,
    type: AttendanceType,
    date: Date
  ): Promise<boolean> {
    try {
      // Create date range for the day
      const startOfDay = new Date(date)
      startOfDay.setHours(0, 0, 0, 0)

      const endOfDay = new Date(date)
      endOfDay.setHours(23, 59, 59, 999)

      // Format dates as ISO strings for SQL
      const startDateStr = startOfDay.toISOString()
      const endDateStr = endOfDay.toISOString()

      // Find the record first to confirm it exists
      const [absence] = await db
        .select()
        .from(schema.absences)
        .where(
          and(
            eq(schema.absences.uniqueCode, uniqueCode),
            eq(schema.absences.type, type),
            gte(schema.absences.recordedAt, new Date(startDateStr)),
            lte(schema.absences.recordedAt, new Date(endDateStr))
          )
        )
        .limit(1)

      if (!absence) {
        return false // Record not found
      }

      // Delete the record
      await db
        .delete(schema.absences)
        .where(
          and(
            eq(schema.absences.uniqueCode, uniqueCode),
            eq(schema.absences.type, type),
            gte(schema.absences.recordedAt, new Date(startDateStr)),
            lte(schema.absences.recordedAt, new Date(endDateStr))
          )
        )

      return true // Record deleted successfully
    } catch (error) {
      console.error('Error in deleteByUniqueCodeAndTypeAndDate:', error)
      return false
    }
  }

  /**
   * Delete all absences for a student by unique code
   */
  async deleteAllByUniqueCode(uniqueCode: string): Promise<void> {
    try {
      // Delete all attendance records for this student
      await db.delete(schema.absences).where(eq(schema.absences.uniqueCode, uniqueCode))

      console.info(`Deleted all attendance records for student with uniqueCode: ${uniqueCode}`)
    } catch (error) {
      console.error(`Error deleting attendance records for student ${uniqueCode}:`, error)
      throw error
    }
  }

  /**
   * Get recent attendance records for debugging
   */
  async getRecentAttendanceRecords(limit: number = 10): Promise<Absence[]> {
    try {
      const records = await db
        .select()
        .from(schema.absences)
        .orderBy(desc(schema.absences.recordedAt))
        .limit(limit)

      return records.map(record => this.mapToAbsence(record))
    } catch (error) {
      console.error('Error getting recent attendance records:', error)
      return []
    }
  }

  /**
   * Map a database absence to an Absence entity
   */
  private mapToAbsence(absence: any): Absence {
    return {
      id: absence.id,
      uniqueCode: absence.uniqueCode,
      type: absence.type,
      recordedAt: absence.recordedAt,
      createdAt: absence.createdAt,
      reason: absence.reason || undefined, // Include reason if present
    }
  }
}
